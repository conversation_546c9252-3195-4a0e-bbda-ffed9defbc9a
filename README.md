# تمسيك - منصة إسلامية شاملة

منصة تمسيك هي منصة إسلامية تهدف إلى مساعدة الخطباء والدعاة في إعداد وتنظيم خطبهم ودروسهم بطريقة احترافية، مع التركيز على العلماء والمحاضرات في اليمن.

## 🚀 الميزات الرئيسية

### 📚 إدارة المحتوى
- **الخطب الجاهزة**: مكتبة شاملة من الخطب في مختلف المواضيع
- **الفتاوى**: منصة للأسئلة والفتاوى من العلماء اليمنيين
- **المحاضرات والدروس**: جدولة المحاضرات حسب المحافظات اليمنية
- **المفكرون والدعاة**: قاعدة بيانات شاملة للمفكرين الإسلاميين

### 👥 إدارة المستخدمين
- **نظام مصادقة متقدم**: تسجيل دخول آمن مع JWT
- **أدوار متعددة**: مستخدم عادي، عالم، مدير
- **ملفات شخصية**: إدارة البيانات الشخصية والتخصصات

### 🔧 إدارة النظام
- **لوحة تحكم إدارية**: إدارة شاملة للمحتوى والمستخدمين
- **نظام تصنيفات**: تنظيم المحتوى بتصنيفات هرمية
- **نشرة بريدية**: إدارة المشتركين والإشعارات

## 🛠️ التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الويب
- **MySQL** - قاعدة البيانات
- **JWT** - المصادقة والتحقق
- **bcryptjs** - تشفير كلمات المرور

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript** - التفاعل والديناميكية
- **Font Awesome** - الأيقونات

## 📋 متطلبات النظام

- **Node.js** (الإصدار 16 أو أحدث)
- **MySQL** (الإصدار 8.0 أو أحدث)
- **npm** أو **yarn**

## ⚙️ التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-username/tamsik.git
cd tamsik
```

### 2. تثبيت الحزم
```bash
npm install
```

### 3. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات MySQL جديدة
2. نسخ ملف `.env.example` إلى `.env`
3. تحديث إعدادات قاعدة البيانات في `.env`:

```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=tamsik_db
DB_PORT=3306

# إعدادات JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# إعدادات الخادم
PORT=3000
NODE_ENV=development
```

### 4. إنشاء الجداول والبيانات الأولية
```bash
node scripts/setupDatabase.js
```

### 5. تشغيل الخادم
```bash
# للتطوير
npm run dev

# للإنتاج
npm start
```

## 📡 API Documentation

### المصادقة
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - الحصول على بيانات المستخدم الحالي
- `PUT /api/auth/me` - تحديث بيانات المستخدم

### العلماء
- `GET /api/scholars` - الحصول على جميع العلماء
- `GET /api/scholars/featured` - العلماء المميزون
- `GET /api/scholars/:id` - عالم محدد
- `POST /api/scholars` - إنشاء عالم جديد (مدير فقط)

### الفتاوى
- `GET /api/fatwas` - الحصول على جميع الفتاوى
- `GET /api/fatwas/featured` - الفتاوى المميزة
- `POST /api/fatwas` - إرسال سؤال جديد
- `GET /api/fatwas/:id` - فتوى محددة

### الخطب
- `GET /api/sermons` - الحصول على جميع الخطب
- `GET /api/sermons/featured` - الخطب المميزة
- `POST /api/sermons` - إنشاء خطبة جديدة
- `GET /api/sermons/:id` - خطبة محددة

### المحاضرات
- `GET /api/lectures` - الحصول على جميع المحاضرات
- `GET /api/lectures/province/:province` - محاضرات حسب المحافظة
- `GET /api/lectures/stats` - إحصائيات المحاضرات
- `POST /api/lectures` - إنشاء محاضرة جديدة

## 🗂️ هيكل المشروع

```
tamsik/
├── config/
│   ├── database.js          # إعدادات قاعدة البيانات
│   └── createTables.js      # تعريف الجداول
├── middleware/
│   ├── auth.js              # مصادقة المستخدمين
│   └── validation.js        # التحقق من البيانات
├── models/
│   ├── User.js              # نموذج المستخدمين
│   ├── Scholar.js           # نموذج العلماء
│   ├── Fatwa.js             # نموذج الفتاوى
│   ├── Sermon.js            # نموذج الخطب
│   ├── Lecture.js           # نموذج المحاضرات
│   └── ...
├── routes/
│   ├── auth.js              # مسارات المصادقة
│   ├── scholars.js          # مسارات العلماء
│   ├── fatwas.js            # مسارات الفتاوى
│   └── ...
├── scripts/
│   └── setupDatabase.js     # إعداد قاعدة البيانات
├── public/                  # الملفات الثابتة
├── uploads/                 # ملفات المستخدمين
├── .env                     # متغيرات البيئة
├── server.js                # الخادم الرئيسي
└── server-simple.js         # خادم مبسط للاختبار
```

## 🧪 الاختبار

### اختبار الخادم المبسط
```bash
node server-simple.js
```

### مسارات الاختبار
- `GET http://localhost:3000/api/health` - حالة الخادم
- `GET http://localhost:3000/api/test-db` - اختبار قاعدة البيانات
- `POST http://localhost:3000/api/setup-db` - إعداد قاعدة البيانات
- `POST http://localhost:3000/api/test-auth` - اختبار المصادقة

### بيانات الاختبار
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- مصادقة JWT آمنة
- حماية من CORS
- تحديد معدل الطلبات (Rate Limiting)
- التحقق من صحة البيانات

## 📝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة ISC - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: [www.tamsik.com](https://www.tamsik.com)

---

**تمسيك** - "والذين يمسكون بالكتاب وأقاموا الصلاة إنا لا نضيع أجر المصلحين"
